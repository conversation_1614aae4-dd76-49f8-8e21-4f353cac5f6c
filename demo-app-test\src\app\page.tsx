'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { UploadDialog } from '@/components/upload-dialog';

interface Candidate {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  domain: string;
  source: string;
  skills: { name: string }[];
  currentStatus: { name: string };
}

export default function Home() {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDomain, setSelectedDomain] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  useEffect(() => {
    fetchCandidates();
  }, [selectedDomain, selectedStatus]);

  const fetchCandidates = async () => {
    try {
      let url = '/api/candidates';
      const params = new URLSearchParams();
      
      if (selectedDomain) params.append('domain', selectedDomain);
      if (selectedStatus) params.append('status', selectedStatus);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url);
      const data = await response.json();
      setCandidates(data);
    } catch (error) {
      console.error('Error fetching candidates:', error);
    }
  };

  const filteredCandidates = candidates.filter(candidate => {
    const searchString = `${candidate.firstName} ${candidate.lastName} ${candidate.email} ${candidate.domain} ${candidate.skills.map(s => s.name).join(' ')}`.toLowerCase();
    return searchString.includes(searchTerm.toLowerCase());
  });

  return (
    <main className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">ATS Dashboard</h1>
        <UploadDialog />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Input
          placeholder="Search candidates..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <select
          className="border rounded-md p-2"
          value={selectedDomain}
          onChange={(e) => setSelectedDomain(e.target.value)}
        >
          <option value="">All Domains</option>
          <option value="Frontend">Frontend</option>
          <option value="Backend">Backend</option>
          <option value="Full Stack">Full Stack</option>
          <option value="DevOps">DevOps</option>
        </select>
        <select
          className="border rounded-md p-2"
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
        >
          <option value="">All Statuses</option>
          <option value="Resume Uploaded">Resume Uploaded</option>
          <option value="Under Screening">Under Screening</option>
          <option value="Interview Scheduled">Interview Scheduled</option>
          <option value="Selected">Selected</option>
          <option value="Rejected">Rejected</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredCandidates.map((candidate) => (
          <Card key={candidate.id}>
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h2 className="text-xl font-semibold">
                  {candidate.firstName} {candidate.lastName}
                </h2>
                <Badge variant="secondary">{candidate.currentStatus.name}</Badge>
              </div>
              <p className="text-sm text-gray-600 mb-2">{candidate.email}</p>
              <p className="text-sm text-gray-600 mb-2">{candidate.phone}</p>
              <div className="mb-2">
                <Badge variant="outline" className="mr-2">{candidate.domain}</Badge>
                <Badge variant="outline" className="mr-2">{candidate.source}</Badge>
              </div>
              <div className="flex flex-wrap gap-1">
                {candidate.skills.map((skill, index) => (
                  <Badge key={index} variant="secondary" className="mr-1">
                    {skill.name}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </main>
  );
    </div>
  );
}

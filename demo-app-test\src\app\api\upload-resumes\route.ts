import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { writeFile } from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const sources = formData.getAll('sources') as string[];

    const uploadDir = path.join(process.cwd(), 'uploads');

    const results = await Promise.all(
      files.map(async (file, index) => {
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);

        // Create unique filename
        const filename = `${Date.now()}-${file.name}`;
        const filepath = path.join(uploadDir, filename);

        // Save file
        await writeFile(filepath, buffer);

        // TODO: Implement resume parsing logic here
        // For now, we'll use placeholder data
        const parsedData = {
          firstName: 'John',
          lastName: 'Doe',
          email: `candidate${Date.now()}@example.com`,
          phone: '+1234567890',
          domain: 'Full Stack',
          skills: ['JavaScript', 'React', 'Node.js']
        };

        // Create candidate record
        const candidate = await prisma.candidate.create({
          data: {
            firstName: parsedData.firstName,
            lastName: parsedData.lastName,
            email: parsedData.email,
            phone: parsedData.phone,
            domain: parsedData.domain,
            source: sources[index],
            resumeUrl: filepath,
            skills: {
              connectOrCreate: parsedData.skills.map(skill => ({
                where: { name: skill },
                create: { name: skill }
              }))
            },
            currentStatus: {
              connectOrCreate: {
                where: { name: 'Resume Uploaded' },
                create: { name: 'Resume Uploaded' }
              }
            }
          },
          include: {
            skills: true,
            currentStatus: true
          }
        });

        return candidate;
      })
    );

    return NextResponse.json({
      message: 'Resumes uploaded successfully',
      candidates: results
    });
  } catch (error) {
    console.error('Error uploading resumes:', error);
    return NextResponse.json(
      { error: 'Failed to upload resumes' },
      { status: 500 }
    );
  }
}